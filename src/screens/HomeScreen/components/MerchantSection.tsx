import { Templates } from "@/types/promotion";
import R18RewardVoucherV2 from "@/components/ModuleFederation/R18RewardVoucherV2";
import R18ProductPageSlider from "@/components/ModuleFederation/R18ProductPageSlider";
import { useTracking } from "@/hooks/useTracking";
import { HomeScreen, ScreenId } from "../HomeTrackingId";
import { accountStore } from "@/store/accountStore";
import { INSTALLMENT_PRODUCT_INVENTORY_ID, INSTALLMENT_REWARD_INVENTORY_ID } from "@/screens/OnboardingScreen/constant";
import { useState, useRef, useEffect, memo, useCallback, useMemo } from "react";

export const MerchantSection = () => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";

  // State to track loading status of components
  const [loadedComponents, setLoadedComponents] = useState({
    rewardVoucher: false,
    productSlider: false,
  });

  // Ref for the section container
  const sectionRef = useRef<HTMLElement>(null);

  // Check if all components are loaded
  const allComponentsLoaded = loadedComponents.rewardVoucher && loadedComponents.productSlider;
  console.log("allComponentsLoaded", allComponentsLoaded);
  // Effect to handle height animation - only run once when all components are loaded
  useEffect(() => {
    if (allComponentsLoaded && sectionRef.current) {

      const section = sectionRef.current;

      // Get the natural height by temporarily removing height restriction
      section.style.height = 'auto';
      section.style.overflow = 'visible';
      const naturalHeight = section.scrollHeight;
      console.log("naturalHeight", naturalHeight)

      // Reset to 0 and animate to natural height
      section.style.height = '0px';
      section.style.overflow = 'hidden';
      section.style.opacity = '0';
      section.style.filter = 'blur(4px)';
      section.style.transition = 'none';

      // Use requestAnimationFrame to ensure the height change is applied
      requestAnimationFrame(() => {
        section.style.transition = 'height 0.5s ease-in-out, opacity 0.5s ease-in-out, filter 0.5s ease-in-out';
        section.style.height = `${naturalHeight}px`;
        section.style.opacity = '1';
        section.style.filter = 'blur(0px)';

        // Remove height restriction after animation completes
        setTimeout(() => {
          section.style.height = 'auto';
          section.style.overflow = 'visible';
        }, 500);
      });
    }
  }, [allComponentsLoaded]);

  const onLoadProductPageSlider = (data: any) => {
    console.log("onLoadProductPageSlider", data);
    setLoadedComponents((prev) => ({ ...prev, productSlider: true }));
    trackEvent(HomeScreen.LoadMerchantComponent, {
      installment_account_status,
    });
  };

  const onLoadRewardVoucherV2 = (data: any) => {
    console.log("onLoadRewardVoucherV2", data);
    setLoadedComponents((prev) => ({ ...prev, rewardVoucher: true }));
    trackEvent(HomeScreen.LoadVoucherComponent, {
      installment_account_status,
    });
  };

  const renderAds = useMemo(() => {
    return (
      <>
        <R18RewardVoucherV2
          className="[&>div>section>div>h3]:!text-base w-full mb-3 overflow-hidden empty:hidden"
          reward_inventory_id={"osc_retail_mart_4"}
          onLoaded={onLoadRewardVoucherV2}
          hasSkeleton={false}
        />
        <R18ProductPageSlider
          request={{
            inventory_id: "Promotion_Hub_Top",
            extra_infos: {
              position: "installment_homepage",
            },
          }}
          className="[&>div>div>h3]:!text-base [&>div>div>div]:!text-base empty:hidden mx-4 mb-4 p-4 pt-4.5 bg-white rounded-extra-lg"
          onLoaded={onLoadProductPageSlider}
          template={Templates.LIST11}
          containerPadding={16}
          hasSkeleton={false}
        />
      </>
    );
  }, []);

  return (
    <>
      <section
        ref={sectionRef}
        style={{
          height: allComponentsLoaded ? "auto" : "0px",
          overflow: allComponentsLoaded ? "visible" : "hidden",
        }}>
        {renderAds}
      </section>
    </>
  );
};
